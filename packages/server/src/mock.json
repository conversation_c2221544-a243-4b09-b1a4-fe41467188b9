{"code": "00000", "message": "分页查询成功", "data": {"code": "00000", "total": 2, "list": [{"pageList": [{"id": "1730097774UV0NbCkV", "name": "首页", "description": "", "createTime": "2024-10-28 14:42:54", "updateTime": "2024-10-30 17:14:45", "configure": {"background": {"mode": "pinned"}}, "widgetList": [{"id": "1730173476hhA2C5mJ", "componentName": "SwiperWidget", "title": "轮播组件", "props": {"list": [{"id": "1111", "title": "今日推荐", "image": "http://www.sunmao-design.top/sunmao/image/d0b188d9-5cd4-4374-8e42-efaecee92a9f.jpg"}, {"id": "2222", "title": "热门榜单", "image": "http://www.sunmao-design.top/sunmao/image/aff1638c-c7cb-4a46-97a2-8f1870bb684a.jpg"}], "showTitle": false, "height": "200", "indicatorMode": "line", "indicatorActiveColor": "#FFFFFF", "autoplay": true}, "configure": {"design": {"types": ["h5", "wechat"]}}}, {"id": "1730106511lwQlaCgk", "componentName": "NoticeWidget", "title": "公告", "props": {"text": "萌宠社交，发帖记录你的萌宠瞬间吧！ - 云搭万物", "mode": "base", "textColor": "#341602FF", "cardBg": "#FFC102FF", "cardRadius": "round", "cardShadow": true, "upperLowerMargin": "10", "pageMargin": "10"}, "configure": {"design": {"types": ["h5", "wechat"]}}}, {"id": "1730106080nY0m4iaP", "componentName": "NavigationWidget", "title": "导航组件", "props": {"list": [{"id": "1111", "name": "萌宠介绍", "image": "http://www.sunmao-design.top/sunmao/image/9f74e528-0620-4bb4-a6ba-4e353ed8d2d0.jpg", "route": {"name": "发布萌宠信息", "id": "1730106670eo49eWZc", "type": "page"}}, {"id": "2222", "name": "萌宠日记", "image": "http://www.sunmao-design.top/sunmao/image/40f2acb4-c0ff-4983-9911-3dd52da580b0.jpg", "route": {"name": "发布萌宠日记", "id": "1730108990hV9kiYBX", "type": "page"}}, {"id": "3333", "name": "萌宠百科", "image": "http://www.sunmao-design.top/sunmao/image/25adfa6d-332b-41ff-82da-818e8d9f1346.jpg"}, {"id": "4444", "name": "萌宠调查", "image": "http://www.sunmao-design.top/sunmao/image/316b4df2-0e5d-482d-b733-136cc81eee84.jpg"}], "mode": "multi", "shape": "square", "colNumber": "4", "textColor": "#373535", "backgroundColor": "#ffffff", "backgroundShape": "square", "backgroundMargin": "0"}, "configure": {"design": {"types": ["h5", "wechat"]}}}, {"id": "1730108496YGBDuRgk", "componentName": "ContainerTabsWidget", "title": "标签页组件", "props": {"tab": {"activeId": "item-2", "list": [{"id": "item-1", "name": "萌宠介绍"}, {"id": "item-2", "name": "记录瞬间"}]}}, "configure": {"component": {"isTab": true, "tabProp": "tab"}, "design": {"types": ["h5", "wechat"]}}, "isTab": true, "tabProp": "tab", "children": [{"componentName": "ItemInTabsWidget", "id": "item-1", "isPrivate": true, "props": {"tabId": "item-1"}, "children": [{"id": "1730106651ruFD2DDu", "componentName": "CustomListWIdget", "title": "表单列表组件", "props": {"formId": "1730105543OAuEChHN"}, "configure": {"design": {"types": ["h5", "wechat"]}}}]}, {"componentName": "ItemInTabsWidget", "id": "item-2", "isPrivate": true, "props": {"tabId": "item-2"}, "children": [{"id": "17301090628vGhM7Qa", "componentName": "CustomListWIdget", "title": "表单列表组件", "props": {"formId": "1730108807sjoDxeY5"}, "configure": {"design": {"types": ["h5", "wechat"]}}}]}]}]}, {"id": "1730097774o6a6UV4f", "name": "推荐", "description": "", "createTime": "2024-10-28 14:42:54", "updateTime": "2024-10-30 17:17:46", "configure": {"background": {"mode": "pinned"}}, "widgetList": [{"id": "1730279733QwwI3pNZ", "componentName": "HotSpotImgWidget", "title": "热区切图组件", "props": {"hotSpot": {"image": "http://www.sunmao-design.top/image/recommend/Lx6xV00pvvAp.jpg", "hotSpotList": [{"id": "1730279758tGL3BDy4", "componentName": "text", "title": "文本", "sign": 1, "w": 123, "h": 31, "x": 120, "y": 90, "props": {"text": "宠物日记", "size": "30", "lineHieght": "22", "color": "#FFFFFFFF", "bold": true}}, {"id": "1730279814VpUsfaub", "componentName": "image", "title": "图片", "sign": 2, "w": 100, "h": 100, "x": 246, "y": 52, "props": {"url": "http://www.sunmao-design.top/image/recommend/71f503ae52fdfeeb49287a0786597527.png", "radius": "0"}}], "width": 375, "height": 210.9375}}, "configure": {"design": {"types": ["h5", "wechat"]}}}, {"id": "1730113352qqLT1XCc", "componentName": "CustomListWIdget", "title": "表单列表组件", "props": {"formId": "1730108807sjoDxeY5"}, "configure": {"design": {"types": ["h5", "wechat"]}}}]}, {"id": "1730097774thKsleOW", "name": "发现", "description": "", "createTime": "2024-10-28 14:42:54", "updateTime": "2024-10-28 14:42:54", "configure": {"background": {"mode": "pinned"}}, "widgetList": []}, {"id": "1730097774u9B3ZZ82", "name": "灵感", "description": "", "createTime": "2024-10-28 14:42:54", "updateTime": "2024-10-28 14:42:54", "configure": {"background": {"mode": "pinned"}}, "widgetList": []}, {"id": "1730106670eo49eWZc", "name": "发布萌宠信息", "createTime": "2024-10-28 17:11:10", "updateTime": "2024-10-28 17:11:33", "configure": {"background": {"mode": "pinned"}}, "widgetList": [{"id": "1730106677CqDLfjCM", "componentName": "CustomFormWidget", "title": "表单组件", "props": {"formId": "1730105543OAuEChHN"}, "configure": {"design": {"types": ["h5", "wechat"]}}}]}, {"id": "1730108990hV9kiYBX", "name": "发布萌宠日记", "createTime": "2024-10-28 17:49:50", "updateTime": "2024-10-28 17:50:10", "configure": {"background": {"mode": "pinned"}}, "widgetList": [{"id": "1730108996agceyQqq", "componentName": "CustomFormWidget", "title": "表单组件", "props": {"formId": "1730108807sjoDxeY5"}, "configure": {"design": {"types": ["h5", "wechat"]}}}]}], "_id": "671f329348e97316fdfdaacc", "id": "1730097774ZvRqPBGO", "userId": "86ff85fc-e20b-4e6a-86ac-6cfe2b283584", "title": "宠吧小程序", "description": "宠物设计小程序", "createTime": "2024-10-28 14:42:54", "updateTime": "2024-10-28 14:42:54", "recommend": true, "qr": "http://www.sunmao-design.top/sunmao/image/qr-1730097774ZvRqPBGO.jpg", "use": "other", "configure": {"navigation": {"theme": "", "list": [{"name": "首页", "icon": "icon-a-064_zhuye", "path": "pages/index/index", "status": true, "router": {"type": "page", "name": "首页", "id": "1730097774UV0NbCkV"}}, {"name": "推荐", "icon": "icon-a-064_huore", "path": "pages/index/tab1", "status": true, "router": {"type": "page", "name": "推荐", "id": "1730097774o6a6UV4f"}}, {"name": "发现", "icon": "icon-a-064_sousuo", "path": "pages/index/tab2", "status": false, "router": {"type": "page", "name": "发现", "id": "1730097774thKsleOW"}}, {"name": "灵感", "icon": "icon-a-064_shuidi", "path": "pages/index/tab3", "router": {"type": "page", "name": "灵感", "id": "1730097774u9B3ZZ82"}}, {"name": "我的", "icon": "icon-a-064_wode", "path": "pages/index/tab4", "status": true, "router": {"type": "page", "name": "我的", "id": "my"}}], "mode": "floating"}, "advertising": {"status": true, "image": "http://www.sunmao-design.top/sunmao/image/6e0ecf43-9383-4afa-be5c-b5027af734e8.jpg", "time": 3}, "personalCenter": {"backgroundImage": "http://www.sunmao-design.top/image/recommend/TScB8c5U50a7.jpg", "menuList": [{"name": "足迹", "icon": "home-two-d7djo2eo"}, {"name": "足迹", "icon": "home-two-d7djo2eo"}, {"name": "足迹", "icon": "home-two-d7djo2eo"}, {"name": "足迹", "icon": "home-two-d7djo2eo"}], "advertisingImage": "https://image.meiye.art/FpA6jGlyjLv7ET0t_LmFEuCNlEcU"}}, "__v": 0}, {"pageList": [{"id": "1731634631gbUmpCUS", "name": "首页", "description": "上春山首页", "createTime": "2024-11-15 09:37:11", "updateTime": "2024-11-15 10:14:47", "configure": {"background": {"mode": "pinned"}}, "widgetList": [{"id": "1731634641qrbpP1NT", "componentName": "SwiperWidget", "title": "轮播组件", "props": {"list": [{"id": "1111", "title": "今日推荐", "image": "http://www.sunmao-design.top/sunmao/image/c7b87a4b-a7a8-4079-9a4f-479791719e95.jpg"}, {"id": "2222", "title": "热门榜单", "image": "http://www.sunmao-design.top/sunmao/image/02bb80ff-22ee-476c-b2d0-3ad3d5d64ea8.jpg"}, {"id": "3333", "title": "首发新品", "image": "http://www.sunmao-design.top/sunmao/image/4f8ddebd-b862-4305-b988-d7901113d376.jpg"}], "showTitle": false, "height": "200", "indicatorMode": "line", "indicatorActiveColor": "#FFFFFF", "autoplay": true}, "configure": {"design": {"types": ["h5", "wechat"]}}}, {"id": "1731634682LXblVjlb", "componentName": "NavigationWidget", "title": "导航组件", "props": {"list": [{"id": "1111", "name": "景区打卡", "image": "http://www.sunmao-design.top/sunmao/image/22e21b40-cc87-4f71-bc8b-8d9454b9313d.jpg", "route": {"name": "上春山景区打卡", "id": "1731635259hFQWotlg", "type": "page"}}, {"id": "2222", "name": "美食打卡", "image": "http://www.sunmao-design.top/sunmao/image/68bd16df-1969-40bb-af33-bd00bd1b175d.jpg", "route": {"name": "上春山美食打卡", "id": "1731635420CsKK9Sjg", "type": "page"}}, {"id": "3333", "name": "打卡记录", "image": "http://www.sunmao-design.top/sunmao/image/065cdaa2-cf85-45e5-9276-4f9532402fd2.jpg"}, {"id": "4444", "name": "待开发", "image": "http://www.sunmao-design.top/sunmao/image/aac7f241-dfeb-48da-9189-70e8c7f80295.jpg"}], "mode": "multi", "shape": "round", "colNumber": "4", "textColor": "#373535", "backgroundColor": "#ffffff", "backgroundShape": "square", "backgroundMargin": "0"}, "configure": {"design": {"types": ["h5", "wechat"]}}}, {"id": "1731635760BX4Zdj1A", "componentName": "NoticeWidget", "title": "公告", "props": {"text": "东风拂人面，落花作簪，踏遍春山不思还", "mode": "base", "textColor": "#2254f4", "cardBg": "#FFFFFF", "cardRadius": "round", "cardShadow": true, "upperLowerMargin": "10", "pageMargin": "10"}, "configure": {"design": {"types": ["h5", "wechat"]}}}, {"id": "1731634711jIFgRnXP", "componentName": "ImgCubeWidget", "title": "图片魔方组件", "props": {"config": {"mode": "custom", "row": 6, "col": 6, "cubeList": [{"top": 1, "left": 1, "bottom": 5, "right": 4, "height": 4, "width": 3, "image": "http://www.sunmao-design.top/sunmao/image/095148c3-dc43-48fe-872c-372b3af2815b.jpg"}, {"top": 5, "left": 1, "bottom": 7, "right": 4, "height": 2, "width": 3, "image": "http://www.sunmao-design.top/sunmao/image/5595b454-7934-46c7-aef5-8086d45aefc1.jpg"}, {"top": 1, "left": 4, "bottom": 4, "right": 7, "height": 3, "width": 3, "image": "http://www.sunmao-design.top/sunmao/image/40a08fec-c7f1-4347-b92e-225ea25a564a.jpg"}, {"top": 4, "left": 4, "bottom": 7, "right": 7, "height": 3, "width": 3, "image": "http://www.sunmao-design.top/sunmao/image/d025bf0f-b05e-4948-bddf-ea6cb2f0eefc.jpg"}], "label": "自定义"}, "pageMargin": "0", "cubeRadius": "square", "itemMargin": "10", "itemRadius": "square"}, "configure": {"design": {"types": ["h5", "wechat"]}}}, {"id": "1731634756kJoc69O7", "componentName": "ContainerTabsWidget", "title": "标签页组件", "props": {"tab": {"activeId": "item-1", "list": [{"id": "item-1", "name": "景区打卡"}, {"id": "item-2", "name": "美食打卡"}]}}, "configure": {"component": {"isTab": true, "tabProp": "tab"}, "design": {"types": ["h5", "wechat"]}}, "isTab": true, "tabProp": "tab", "children": [{"componentName": "ItemInTabsWidget", "id": "item-1", "isPrivate": true, "props": {"tabId": "item-1"}, "children": [{"id": "1731634764aN66rbPk", "componentName": "CustomListWIdget", "title": "表单列表组件", "props": {"formId": "1731634768fblZJT4J"}, "configure": {"design": {"types": ["h5", "wechat"]}}}]}, {"componentName": "ItemInTabsWidget", "id": "item-2", "isPrivate": true, "props": {"tabId": "item-2"}, "children": [{"id": "17316350059ctgpxb7", "componentName": "CustomListWIdget", "title": "表单列表组件", "props": {"formId": "1731635007GFbosyKG"}, "configure": {"design": {"types": ["h5", "wechat"]}}}]}]}]}, {"id": "17316346315vmeUOGU", "name": "推荐", "description": "", "createTime": "2024-11-15 09:37:11", "updateTime": "2024-11-15 10:18:24", "configure": {"background": {"mode": "pinned", "image": "http://www.sunmao-design.top/sunmao/image/065cdaa2-cf85-45e5-9276-4f9532402fd2.jpg", "color": "rgb(178, 160, 159)"}}, "widgetList": [{"id": "1731637039OfsMa3YG", "componentName": "SegmentationWidget", "title": "空白组件", "props": {"height": "260"}, "configure": {"design": {"types": ["h5", "wechat", "article", "form-detail"]}}}, {"id": "1731637045B5cGfyoE", "componentName": "TextWidget", "title": "文本组件", "props": {"text": "我们一起上春山", "size": "24", "bold": true, "lineHeight": "24", "textColor": "#FFFFFFFF", "location": "center", "colSpacing": "10"}, "configure": {"design": {"types": ["h5", "wechat", "article"]}}}, {"id": "1731637092cxwKjIAQ", "componentName": "CustomListWIdget", "title": "表单列表组件", "props": {"formId": "1731634768fblZJT4J"}, "configure": {"design": {"types": ["h5", "wechat"]}}}]}, {"id": "1731634631CkEFEPsW", "name": "发现", "description": "", "createTime": "2024-11-15 09:37:11", "updateTime": "2024-11-15 09:37:11", "configure": {"background": {"mode": "pinned"}}, "widgetList": []}, {"id": "1731634631ajc3w7g7", "name": "灵感", "description": "", "createTime": "2024-11-15 09:37:11", "updateTime": "2024-11-15 09:37:11", "configure": {"background": {"mode": "pinned"}}, "widgetList": []}, {"id": "1731635259hFQWotlg", "name": "上春山景区打卡", "createTime": "2024-11-15 09:47:39", "updateTime": "2024-11-15 09:50:16", "configure": {"background": {"mode": "pinned"}}, "widgetList": [{"id": "1731635278NikXIE04", "componentName": "HotSpotImgWidget", "title": "热区切图组件", "props": {"hotSpot": {"image": "http://www.sunmao-design.top/image/recommend/yqpaZLmklpg9.jpg", "hotSpotList": [{"id": "17316353055u7Fqwqi", "componentName": "text", "title": "文本", "sign": 1, "w": 149, "h": 27, "x": 113, "y": 96, "props": {"text": "新增景区打卡", "size": "24", "lineHieght": "22", "color": "#FFFFFFFF", "bold": true}}], "width": 375, "height": 206.25000000000003}}, "configure": {"design": {"types": ["h5", "wechat"]}}}, {"id": "1731635382nEBjY7ym", "componentName": "CustomFormWidget", "title": "表单组件", "props": {"formId": "1731634768fblZJT4J"}, "configure": {"design": {"types": ["h5", "wechat"]}}}]}, {"id": "1731635420CsKK9Sjg", "name": "上春山美食打卡", "createTime": "2024-11-15 09:50:20", "updateTime": "2024-11-15 09:51:44", "configure": {"background": {"mode": "pinned"}}, "widgetList": [{"id": "1731635426U0w7kmGx", "componentName": "HotSpotImgWidget", "title": "热区切图组件", "props": {"hotSpot": {"image": "http://www.sunmao-design.top/image/recommend/kRNRfQ4F6Hb5.jpg", "hotSpotList": [{"id": "17316354459nPjKEgu", "componentName": "text", "title": "文本", "sign": 1, "w": 182, "h": 46, "x": 101, "y": 97, "props": {"text": "上春山美食打卡", "size": "24", "lineHieght": "22", "color": "#FFFFFFFF", "bold": true}}], "width": 375, "height": 210.91113610798652}}, "configure": {"design": {"types": ["h5", "wechat"]}}}, {"id": "1731635485p4p4JKWo", "componentName": "CustomFormWidget", "title": "表单组件", "props": {"formId": "1731635007GFbosyKG"}, "configure": {"design": {"types": ["h5", "wechat"]}}}]}, {"id": "1731636892LSgrcbuS", "name": "页面-1731636892pi", "createTime": "2024-11-15 10:14:52", "updateTime": "2024-11-15 10:16:53", "configure": {"background": {"mode": "pinned", "image": "http://www.sunmao-design.top/sunmao/image/065cdaa2-cf85-45e5-9276-4f9532402fd2.jpg", "color": "rgb(178, 160, 159)"}}, "widgetList": [{"id": "1731636928ck4fn0eC", "componentName": "SegmentationWidget", "title": "空白组件", "props": {"height": "260"}, "configure": {"design": {"types": ["h5", "wechat", "article", "form-detail"]}}}, {"id": "1731636942EAKFPlEd", "componentName": "TextWidget", "title": "文本组件", "props": {"text": "我们一起上春山", "size": "20", "bold": true, "lineHeight": "12", "textColor": "#FFFFFFFF", "location": "center", "colSpacing": "10"}, "configure": {"design": {"types": ["h5", "wechat", "article"]}}}]}], "_id": "6736acff48e97316fd03b8d0", "id": "1731634631kStBUdrX", "userId": "86ff85fc-e20b-4e6a-86ac-6cfe2b283584", "title": "上春山小程序", "description": "上春山", "createTime": "2024-11-15 09:37:11", "updateTime": "2024-11-15 09:37:11", "recommend": true, "qr": "http://www.sunmao-design.top/sunmao/image/qr-1731634631kStBUdrX.jpg", "use": "travel", "configure": {"navigation": {"mode": "bottom", "list": [{"name": "首页", "icon": "icon-a-064_zhuye", "path": "pages/index/index", "status": true, "router": {"type": "page", "name": "首页", "id": "1731634631gbUmpCUS"}}, {"name": "推荐", "icon": "icon-a-064_huore", "path": "pages/index/tab1", "status": true, "router": {"type": "page", "name": "推荐", "id": "17316346315vmeUOGU"}}, {"name": "发现", "icon": "icon-a-064_sousuo", "path": "pages/index/tab2", "status": false, "router": {"type": "page", "name": "发现", "id": "1731634631CkEFEPsW"}}, {"name": "灵感", "icon": "icon-a-064_shuidi", "path": "pages/index/tab3", "router": {"type": "page", "name": "灵感", "id": "1731634631ajc3w7g7"}}, {"name": "我的", "icon": "icon-a-064_wode", "path": "pages/index/tab4", "status": true, "router": {"type": "page", "name": "我的", "id": "my"}}]}, "advertising": {"status": true, "image": "http://www.sunmao-design.top/sunmao/image/c8e1a58c-7b2a-4286-882d-1d4815c1681c.jpg", "time": 3}, "personalCenter": {"backgroundImage": "http://www.sunmao-design.top/image/recommend/TScB8c5U50a7.jpg", "menuList": [{"name": "足迹", "icon": "home-two-d7djo2eo"}, {"name": "足迹", "icon": "home-two-d7djo2eo"}, {"name": "足迹", "icon": "home-two-d7djo2eo"}, {"name": "足迹", "icon": "home-two-d7djo2eo"}], "advertisingImage": "https://image.meiye.art/FpA6jGlyjLv7ET0t_LmFEuCNlEcU"}}, "__v": 0}]}}