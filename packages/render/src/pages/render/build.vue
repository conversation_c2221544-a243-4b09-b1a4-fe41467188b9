<template>
  <div class="build-container">
    <div 
      class="build-canvas"
      @dragover="handleDragover"
      @drop="handleDrop"
    >
      <component
        v-for="(widget, index) in widgetList"
        :key="index"
        :is="widget.componentName"
        :style="{
          position: 'absolute',
          left: `${widget.x}px`,
          top: `${widget.y}px`,
          width: `${widget.w}px`,
          height: `${widget.h}px`,
          zIndex: index
        }"
        v-bind="widget.props"
        @click.stop="handleSelectWidget(widget)"
        @contextmenu.prevent="handleContextMenu($event, widget)"
      ></component>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

// 组件列表
const widgetList = ref([]);
// 当前拖拽状态
const dragState = ref({
  isDragging: false,
  component: null
});

// 监听父窗口消息
onMounted(() => {
  window.addEventListener('message', handleMessage);
});

onUnmounted(() => {
  window.removeEventListener('message', handleMessage);
});

// 处理来自父窗口的消息
function handleMessage(event) {
  const { event: eventName, params } = event.data || {};
  
  console.log('接收到消息:', eventName, params);
  
  switch(eventName) {
    case 'dragstart':
      dragState.value = {
        isDragging: true,
        component: params
      };
      console.log('开始拖拽组件:', params);
      break;
      
    case 'dragend':
      dragState.value.isDragging = false;
      console.log('结束拖拽');
      break;
      
    case 'dragover':
      if (dragState.value.isDragging && dragState.value.component) {
        console.log('拖拽位置:', params);
      }
      break;
      
    case 'watchWidgetList':
      widgetList.value = params || [];
      console.log('更新组件列表:', widgetList.value);
      break;
  }
}

// 处理拖拽经过
function handleDragover(e) {
  e.preventDefault();
  e.stopPropagation();

  if (!dragState.value.isDragging) return;

  console.log('Dragover event triggered'); // 调试信息

  // 允许放置元素
  e.dataTransfer.dropEffect = 'move';
}

// 处理拖拽放置
function handleDrop(e) {
  e.preventDefault();
  e.stopPropagation();

  console.log('Drop event triggered'); // 调试信息

  if (!dragState.value.isDragging || !dragState.value.component) {
    console.log('Drag state is not valid'); // 调试信息
    return;
  }

  const component = dragState.value.component;
  const x = e.offsetX;
  const y = e.offsetY;

  // 创建新组件
  const newWidget = {
    ...component,
    x,
    y,
    id: generateId(),
    props: { ...component.props } // 确保 props 被正确复制
  };

  console.log('New widget created:', newWidget); // 调试信息

  // 添加到组件列表
  widgetList.value.push(newWidget);

  console.log('Widget list updated:', widgetList.value); // 调试信息

  // 向父窗口发送更新消息
  sendMessage('syncWidgetList', widgetList.value);

  // 重置拖拽状态
  dragState.value = {
    isDragging: false,
    component: null
  };

  console.log('Drag state reset'); // 调试信息
}

// 选中组件
function handleSelectWidget(widget) {
  sendMessage('setCurrentWidget', widget);
}

// 右键菜单
function handleContextMenu(e, widget) {
  sendMessage('contextmenu', {
    show: true,
    x: e.offsetX,
    y: e.offsetY,
    widgetId: widget.id
  });
}

// 向父窗口发送消息
function sendMessage(event, params) {
  window.parent.postMessage({ event, params }, '*');
}

// 生成唯一ID
function generateId() {
  return 'widget_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
}
</script>

<style scoped>
.build-container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

.build-canvas {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #f5f5f5;
}

.widget {
  user-select: none;
  visibility: visible;
  display: block; /* 确保组件可见 */
}
</style> 
